\documentclass[11pt,a4paper,twocolumn]{article}
\usepackage[UTF8,scheme=plain,fontset=fandol]{ctex}
\usepackage[margin=2cm]{geometry}
\usepackage{minted}
\usepackage{fancyhdr}
\usepackage{tocloft}
\usepackage{titlesec}
\usepackage{xcolor}
\usepackage{graphicx}
\usepackage{hyperref}
\usepackage{multicol}

% 代码高亮设置
\usemintedstyle{colorful}
\setminted{
    fontsize=\footnotesize,
    linenos=true,
    breaklines=true,
    frame=lines,
    framesep=2mm,
    baselinestretch=1.0,
    fontfamily=tt
}

% 页眉页脚设置
\pagestyle{fancy}
\fancyhf{}
\fancyhead[L]{\leftmark}
\fancyhead[R]{\thepage}
\renewcommand{\headrulewidth}{0.4pt}

% 标题格式设置
\titleformat{\section}
{\Large\bfseries\color{blue!70!black}}
{\thesection}{1em}{}

\titleformat{\subsection}
{\large\bfseries\color{green!60!black}}
{\thesubsection}{1em}{}

% 目录设置
\renewcommand{\cftsecleader}{\cftdotfill{\cftdotsep}}

% 超链接设置
\hypersetup{
    colorlinks=true,
    linkcolor=blue,
    filecolor=magenta,
    urlcolor=cyan,
    citecolor=red
}

% 文档信息
\title{\Huge\textbf{ACM竞赛编程模板集}}
\author{\Large 竞赛编程模板}
\date{\today}

\begin{document}

% 封面
\onecolumn
\maketitle
\thispagestyle{empty}

\vspace{2cm}
\begin{center}
\Large
本文档包含了ACM竞赛编程中常用的算法和数据结构模板，\\
涵盖数据结构、图论、数学、字符串处理等多个领域。\\
\vspace{1cm}
所有代码均经过测试验证，可直接用于竞赛。
\end{center}

\newpage

% 目录
\tableofcontents
\thispagestyle{empty}

\newpage
\twocolumn

% 基础模板
\section{基础模板}
\markboth{基础模板}{基础模板}

\subsection{类型定义}
\inputminted{cpp}{types.hpp}

\subsection{位运算工具}
\inputminted{cpp}{bit.hpp}

\subsection{通用工具}
\inputminted{cpp}{tool.hpp}

\subsection{输入输出优化}
\inputminted{cpp}{iostream.hpp}

\subsection{调试工具}
\inputminted{cpp}{debug.hpp}

\subsection{完整模板}
\inputminted{cpp}{all.hpp}

% 数据结构
\section{数据结构}
\markboth{数据结构}{数据结构}

\subsection{线段树}
\inputminted{cpp}{DS/SegmentTree.hpp}

\subsection{懒惰线段树}
\inputminted{cpp}{DS/LazySegmentTree.hpp}

\subsection{树状数组}
\inputminted{cpp}{DS/BinaryIndexedTree.hpp}

\subsection{稀疏表}
\inputminted{cpp}{DS/SparseTable.hpp}

\subsection{并查集}
\inputminted{cpp}{DS/DisjointSetUnion.hpp}

\subsection{可撤销并查集}
\inputminted{cpp}{DS/ErasableDisjointSetUnion.hpp}

\subsection{二进制字典树}
\inputminted{cpp}{DS/BinaryTrie.hpp}

\subsection{离散化}
\inputminted{cpp}{DS/Discretizer.hpp}

% 图论算法
\section{图论算法}
\markboth{图论算法}{图论算法}

\subsection{Dijkstra最短路}
\inputminted{cpp}{Graph/Dijkstra.hpp}

\subsection{Floyd-Warshall全源最短路}
\inputminted{cpp}{Graph/FloydWarshall.hpp}

\subsection{Floyd算法}
\inputminted{cpp}{Graph/Floyd.hpp}

\subsection{拓扑排序}
\inputminted{cpp}{Graph/TopologicalSort.hpp}

\subsection{匈牙利算法}
\inputminted{cpp}{Graph/Hungarian.hpp}

% 数学算法
\section{数学算法}
\markboth{数学算法}{数学算法}

\subsection{快速幂}
\inputminted{cpp}{Math/FastPower.hpp}

\subsection{模运算}
\inputminted{cpp}{Math/ModIntegral.hpp}

\subsection{动态模运算}
\inputminted{cpp}{Math/DyModIntegral.hpp}

\subsection{Barrett约简}
\inputminted{cpp}{Math/Barrett.hpp}

\subsection{组合数学}
\inputminted{cpp}{Math/Combination.hpp}

\subsection{矩阵运算}
\inputminted{cpp}{Math/Matrix.hpp}

\subsection{Miller-Rabin素性测试}
\inputminted{cpp}{Math/MillerRabin.hpp}

\subsection{Pollard-Rho因数分解}
\inputminted{cpp}{Math/PollardRho.hpp}

\subsection{线性筛}
\inputminted{cpp}{Math/Sieve.hpp}

\subsection{辛普森积分}
\inputminted{cpp}{Math/Simpson.hpp}

\subsection{线性基}
\inputminted{cpp}{Math/XorBase.hpp}

\subsection{前缀异或}
\inputminted{cpp}{Math/prexor.hpp}

% 字符串算法
\section{字符串算法}
\markboth{字符串算法}{字符串算法}

\subsection{Manacher算法}
\inputminted{cpp}{String/Manacher.hpp}

\subsection{字符串哈希}
\inputminted{cpp}{String/StringHash.hpp}

% 杂项工具
\section{杂项工具}
\markboth{杂项工具}{杂项工具}

\subsection{大整数}
\inputminted{cpp}{Misc/BigInteger.hpp}

\subsection{动态位集}
\inputminted{cpp}{Misc/DynamicBitset.hpp}

\subsection{快速输入输出}
\inputminted{cpp}{Misc/FastIO.hpp}

\subsection{分数类}
\inputminted{cpp}{Misc/Fraction.hpp}

\subsection{计算几何}
\inputminted{cpp}{Misc/Geometry.hpp}

\subsection{计时器}
\inputminted{cpp}{Misc/Timer.hpp}

\subsection{搜索算法}
\inputminted{cpp}{Misc/search.hpp}

\subsection{对拍工具}
\inputminted{cpp}{Misc/对拍.hpp}

\end{document}
